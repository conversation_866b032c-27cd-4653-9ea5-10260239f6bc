PODS:
  - audio_session (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_tts (0.0.1):
    - Flutter
  - flutter_webrtc (0.9.36):
    - Flutter
    - WebRTC-SDK (= 114.5735.08)
  - Google-Maps-iOS-Utils (5.0.0):
    - GoogleMaps (~> 8.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - speech_to_text (0.0.1):
    - Flutter
    - Try
  - Try (2.1.1)
  - url_launcher_ios (0.0.1):
    - Flutter
  - WebRTC-SDK (114.5735.08)
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - Flutter (from `Flutter`)
  - flutter_tts (from `.symlinks/plugins/flutter_tts/ios`)
  - flutter_webrtc (from `.symlinks/plugins/flutter_webrtc/ios`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - GoogleMaps
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - speech_to_text (from `.symlinks/plugins/speech_to_text/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - Google-Maps-iOS-Utils
    - GoogleMaps
    - Try
    - WebRTC-SDK

EXTERNAL SOURCES:
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  Flutter:
    :path: Flutter
  flutter_tts:
    :path: ".symlinks/plugins/flutter_tts/ios"
  flutter_webrtc:
    :path: ".symlinks/plugins/flutter_webrtc/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  speech_to_text:
    :path: ".symlinks/plugins/speech_to_text/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  audio_session: 088d2483ebd1dc43f51d253d4a1c517d9a2e7207
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_tts: 0f492aab6accf87059b72354fcb4ba934304771d
  flutter_webrtc: 55df3aaa802114dad390191a46c2c8d535751268
  Google-Maps-iOS-Utils: 66d6de12be1ce6d3742a54661e7a79cb317a9321
  google_maps_flutter_ios: e31555a04d1986ab130f2b9f24b6cdc861acc6d3
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  share_plus: c3fef564749587fc939ef86ffb283ceac0baf9f5
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  speech_to_text: b43a7d99aef037bd758ed8e45d79bbac035d2dfe
  Try: 5ef669ae832617b3cee58cb2c6f99fb767a4ff96
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  WebRTC-SDK: c24d2a6c9f571f2ed42297cb8ffba9557093142b
  webview_flutter_wkwebview: 0982481e3d9c78fd5c6f62a002fcd24fc791f1e4

PODFILE CHECKSUM: 8d5c0b2061b967c281088176e97144a4bc54a947

COCOAPODS: 1.16.2
